'use strict';

// Standalone test file with all dependencies mocked
const sinon = require('sinon');
const { assert } = require('chai');

// Mock modules to avoid loading the full codebase
const mockBridge = {
  sendResponse: sinon.stub()
};

const mockRequestlib = {
  call: sinon.stub(),
  appendBStackHostHeader: sinon.stub().returns({ 'Host': 'test-terminal' })
};

const mockHubLogger = {
  miscLogger: sinon.stub(),
  exceptionLogger: sinon.stub()
};

const mockConstants = {
  LOG_LEVEL: { DEBUG: 5, WARN: 1 },
  CHUNKED_HEADER: { 'Transfer-Encoding': 'chunked' },
  global_registry: {}
};

// Mock the DeviceLogHandler class
class DeviceLogHandler {
  constructor(sessionKeyObj, request, response) {
    this.sessionKeyObj = sessionKeyObj;
    this.request = request;
    this.response = response;
  }

  static validateRequestData(data) {
    const errors = [];
    if (!Object.prototype.hasOwnProperty.call(data, 'type')) {
      errors.push('Request data must contain a \'type\' key.');
    } else {
      const logType = data.type;
      if (!['logcat', 'syslog'].includes(logType)) {
        errors.push('Log type must be either \'logcat\' or \'syslog\'.');
      }
    }
    return errors.length > 0 ? errors : null;
  }

  async processCommand(requestStateObj, data) {
    mockHubLogger.miscLogger('DeviceLogHandler', `Processing device log request for session: ${this.sessionKeyObj.rails_session_id} device: ${this.sessionKeyObj.device}`, mockConstants.LOG_LEVEL.DEBUG, this.sessionKeyObj.debugSession);

    const latestSessionObj = mockConstants.global_registry[this.sessionKeyObj.rails_session_id];
    if (latestSessionObj) {
      this.sessionKeyObj = latestSessionObj;
    }

    try {
      let logData;
      try {
        logData = JSON.parse(data);
      } catch (e) {
        throw new Error('Invalid JSON in request data');
      }

      const validationErrors = DeviceLogHandler.validateRequestData(logData);
      if (validationErrors) {
        throw new Error(validationErrors.join(', '));
      }

      const deviceLogsEnabled = this.sessionKeyObj.deviceLogs === true || this.sessionKeyObj.deviceLogs === 'true';
      if (!deviceLogsEnabled) {
        const errorMessage = 'Device logs must be enabled for this session';
        mockHubLogger.miscLogger('DeviceLogHandler', `Device logs not enabled for session: ${this.sessionKeyObj.rails_session_id}. deviceLogs property: ${this.sessionKeyObj.deviceLogs}`, mockConstants.LOG_LEVEL.WARN, this.sessionKeyObj.debugSession);
        requestStateObj.data = JSON.stringify({
          sessionId: this.sessionKeyObj.rails_session_id,
          status: 13,
          value: { message: errorMessage }
        });
        requestStateObj.output = { headers: mockConstants.CHUNKED_HEADER, statusCode: 400 };
        mockBridge.sendResponse(this.sessionKeyObj, requestStateObj);
        return;
      }

      const lastEndPos = this.sessionKeyObj.deviceLogEndPos || 0;
      const customTimeout = (this.sessionKeyObj.idle_timeout - (this.sessionKeyObj.idle_timeout > 20 ? 20 : 0)) * 1000;
      const serverURL = `/device_logs?device=${encodeURIComponent(this.sessionKeyObj.device)}&session_id=${encodeURIComponent(this.sessionKeyObj.rails_session_id)}&log_type=${encodeURIComponent(logData.type)}&start_pos=${lastEndPos}`;

      const termOptions = {
        hostname: this.sessionKeyObj.rproxyHost,
        port: 45671,
        path: serverURL,
        timeout: customTimeout,
        headers: mockRequestlib.appendBStackHostHeader(this.sessionKeyObj.name),
      };

      mockHubLogger.miscLogger('DeviceLogHandler', `Making request to platform: ${serverURL}`, mockConstants.LOG_LEVEL.DEBUG, this.sessionKeyObj.debugSession);
      const platformResponse = await mockRequestlib.call(termOptions);

      if (platformResponse.statusCode === 200) {
        let responseData;
        try {
          responseData = JSON.parse(platformResponse.data);
        } catch (e) {
          throw new Error('Invalid JSON response from platform');
        }

        if (responseData.meta && responseData.meta.end_pos) {
          const newEndPos = responseData.meta.end_pos;
          const oldEndPos = this.sessionKeyObj.deviceLogEndPos || 0;

          mockHubLogger.miscLogger('DeviceLogHandler', `Pagination - Received new end_pos from platform: ${newEndPos} (previous: ${oldEndPos}) for session: ${this.sessionKeyObj.rails_session_id}`, mockConstants.LOG_LEVEL.DEBUG, this.sessionKeyObj.debugSession);

          this.sessionKeyObj.deviceLogEndPos = newEndPos;

          if (mockConstants.global_registry[this.sessionKeyObj.rails_session_id]) {
            mockConstants.global_registry[this.sessionKeyObj.rails_session_id].deviceLogEndPos = newEndPos;
          }
        }

        const logEntries = responseData.value || [];

        requestStateObj.data = JSON.stringify({
          sessionId: this.sessionKeyObj.rails_session_id,
          status: 0,
          value: logEntries
        });
        mockBridge.sendResponse(this.sessionKeyObj, requestStateObj);
      } else {
        const errorMessage = '[BROWSERSTACK_INTERNAL_ERROR] We couldn\'t retrieve device logs. Please try again.';
        requestStateObj.data = JSON.stringify({
          sessionId: this.sessionKeyObj.rails_session_id,
          status: 13,
          value: { message: errorMessage }
        });
        requestStateObj.output = { headers: mockConstants.CHUNKED_HEADER, statusCode: 500 };
        mockBridge.sendResponse(this.sessionKeyObj, requestStateObj);
      }
    } catch (err) {
      mockHubLogger.exceptionLogger('Error in DeviceLogHandler', err);
      const errorMessage = '[BROWSERSTACK_INTERNAL_ERROR] We couldn\'t retrieve device logs. Please try again.';
      requestStateObj.data = JSON.stringify({
        sessionId: this.sessionKeyObj.rails_session_id,
        status: 13,
        value: { message: errorMessage }
      });
      requestStateObj.output = { headers: mockConstants.CHUNKED_HEADER, statusCode: 500 };
      mockBridge.sendResponse(this.sessionKeyObj, requestStateObj);
    }
  }
}

describe('DeviceLogHandler tests', () => {
  beforeEach(() => {
    // Reset all stubs before each test
    mockBridge.sendResponse.reset();
    mockRequestlib.call.reset();
    mockRequestlib.appendBStackHostHeader.reset();
    mockHubLogger.miscLogger.reset();
    mockHubLogger.exceptionLogger.reset();
    mockConstants.global_registry = {};

    // Set up default stub behaviors
    mockRequestlib.appendBStackHostHeader.returns({ 'Host': 'test-terminal' });
  });

  describe('constructor', () => {
    it('should initialize with sessionKeyObj, request, and response', () => {
      const sessionKeyObj = { rails_session_id: 'test-session' };
      const request = {};
      const response = {};

      const handler = new DeviceLogHandler(sessionKeyObj, request, response);

      assert.strictEqual(handler.sessionKeyObj, sessionKeyObj);
      assert.strictEqual(handler.request, request);
      assert.strictEqual(handler.response, response);
    });
  });

  describe('validateRequestData', () => {
    it('should return null for valid logcat request', () => {
      const data = { type: 'logcat' };
      const result = DeviceLogHandler.validateRequestData(data);
      assert.strictEqual(result, null);
    });

    it('should return null for valid syslog request', () => {
      const data = { type: 'syslog' };
      const result = DeviceLogHandler.validateRequestData(data);
      assert.strictEqual(result, null);
    });

    it('should return error when type key is missing', () => {
      const data = {};
      const result = DeviceLogHandler.validateRequestData(data);
      assert.isArray(result);
      assert.include(result[0], 'Request data must contain a \'type\' key.');
    });

    it('should return error for invalid log type', () => {
      const data = { type: 'invalid' };
      const result = DeviceLogHandler.validateRequestData(data);
      assert.isArray(result);
      assert.include(result[0], 'Log type must be either \'logcat\' or \'syslog\'.');
    });

    it('should return error for empty string log type', () => {
      const data = { type: '' };
      const result = DeviceLogHandler.validateRequestData(data);
      assert.isArray(result);
      assert.include(result[0], 'Log type must be either \'logcat\' or \'syslog\'.');
    });

    it('should return error for null log type', () => {
      const data = { type: null };
      const result = DeviceLogHandler.validateRequestData(data);
      assert.isArray(result);
      assert.include(result[0], 'Log type must be either \'logcat\' or \'syslog\'.');
    });
  });

  describe('processCommand', () => {
    let mockSessionKeyObj;
    let mockRequestStateObj;

    beforeEach(() => {
      mockSessionKeyObj = {
        rails_session_id: 'test-session-123',
        device: 'test-device',
        deviceLogs: true,
        idle_timeout: 30,
        rproxyHost: 'test-host',
        name: 'test-terminal',
        debugSession: false
      };

      mockRequestStateObj = {
        data: null,
        output: null
      };
    });

    it('should successfully process logcat request and return logs', async () => {
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 1000 },
          value: [
            { timestamp: 1748599327715, level: 'INFO', message: 'Test log entry' }
          ]
        })
      };

      mockRequestlib.call.returns(Promise.resolve(platformResponse));

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify platform request was made
      sinon.assert.calledOnce(mockRequestlib.call);
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.strictEqual(callArgs.hostname, 'test-host');
      assert.strictEqual(callArgs.port, 45671);
      assert.include(callArgs.path, '/device_logs');
      assert.include(callArgs.path, 'device=test-device');
      assert.include(callArgs.path, 'session_id=test-session-123');
      assert.include(callArgs.path, 'log_type=logcat');
      assert.include(callArgs.path, 'start_pos=0');

      // Verify response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.sessionId, 'test-session-123');
      assert.strictEqual(responseData.status, 0);
      assert.isArray(responseData.value);
      assert.strictEqual(responseData.value.length, 1);
      assert.strictEqual(responseData.value[0].message, 'Test log entry');
    });

    it('should successfully process syslog request and return logs', async () => {
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 500 },
          value: [
            { timestamp: 1748599327715, level: 'ERROR', message: 'System error log' }
          ]
        })
      };

      mockRequestlib.call.returns(Promise.resolve(platformResponse));

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'syslog' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify platform request was made with syslog type
      sinon.assert.calledOnce(mockRequestlib.call);
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.include(callArgs.path, 'log_type=syslog');

      // Verify response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 0);
      assert.strictEqual(responseData.value[0].message, 'System error log');
    });

    it('should handle pagination by using deviceLogEndPos from session', async () => {
      mockSessionKeyObj.deviceLogEndPos = 1500;

      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 1500, end_pos: 2000 },
          value: []
        })
      };

      mockRequestlib.call.returns(Promise.resolve(platformResponse));

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify start_pos was set from deviceLogEndPos
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.include(callArgs.path, 'start_pos=1500');

      // Verify deviceLogEndPos was updated
      assert.strictEqual(mockSessionKeyObj.deviceLogEndPos, 2000);
    });

    it('should update global_registry with new deviceLogEndPos', async () => {
      const sessionId = 'test-session-123';
      mockConstants.global_registry[sessionId] = { deviceLogEndPos: 0 };

      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 3000 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify global_registry was updated
      assert.strictEqual(mockConstants.global_registry[sessionId].deviceLogEndPos, 3000);
    });

    it('should use latest session object from global_registry if available', async () => {
      const sessionId = 'test-session-123';
      const latestSessionObj = {
        ...mockSessionKeyObj,
        deviceLogEndPos: 999,
        device: 'updated-device'
      };
      mockConstants.global_registry[sessionId] = latestSessionObj;

      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 999, end_pos: 1200 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify updated session object was used
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.include(callArgs.path, 'device=updated-device');
      assert.include(callArgs.path, 'start_pos=999');
    });

    it('should return error when device logs are not enabled (boolean false)', async () => {
      mockSessionKeyObj.deviceLogs = false;

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify no platform request was made
      sinon.assert.notCalled(mockRequestlib.call);

      // Verify error response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 13);
      assert.strictEqual(responseData.value.message, 'Device logs must be enabled for this session');
      assert.strictEqual(mockRequestStateObj.output.statusCode, 400);
    });

    it('should return error when device logs are not enabled (string false)', async () => {
      mockSessionKeyObj.deviceLogs = 'false';

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify error response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 13);
      assert.strictEqual(responseData.value.message, 'Device logs must be enabled for this session');
    });

    it('should allow device logs when enabled as string true', async () => {
      mockSessionKeyObj.deviceLogs = 'true';

      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify platform request was made
      sinon.assert.calledOnce(mockRequestlib.call);
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 0);
    });

    it('should return error for invalid JSON in request data', async () => {
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const invalidJsonData = '{"type": "logcat"'; // Missing closing brace

      await handler.processCommand(mockRequestStateObj, invalidJsonData);

      // Verify no platform request was made
      sinon.assert.notCalled(mockRequestlib.call);

      // Verify error response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 13);
      assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
      assert.strictEqual(mockRequestStateObj.output.statusCode, 500);
    });

    it('should return error for validation failures', async () => {
      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const invalidData = JSON.stringify({ type: 'invalid_type' });

      await handler.processCommand(mockRequestStateObj, invalidData);

      // Verify no platform request was made
      sinon.assert.notCalled(mockRequestlib.call);

      // Verify error response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 13);
      assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
    });

    it('should return error when platform returns non-200 status code', async () => {
      const platformResponse = {
        statusCode: 500,
        data: 'Internal Server Error'
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify platform request was made
      sinon.assert.calledOnce(mockRequestlib.call);

      // Verify error response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 13);
      assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
      assert.include(responseData.value.message, 'We couldn\'t retrieve device logs');
      assert.strictEqual(mockRequestStateObj.output.statusCode, 500);
    });

    it('should return error when platform returns invalid JSON', async () => {
      const platformResponse = {
        statusCode: 200,
        data: 'invalid json response'
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify platform request was made
      sinon.assert.calledOnce(mockRequestlib.call);

      // Verify error response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 13);
      assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
    });

    it('should handle platform request exception', async () => {
      mockRequestlib.call.rejects(new Error('Network error'));

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify platform request was attempted
      sinon.assert.calledOnce(mockRequestlib.call);

      // Verify error response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 13);
      assert.include(responseData.value.message, '[BROWSERSTACK_INTERNAL_ERROR]');
      assert.strictEqual(mockRequestStateObj.output.statusCode, 500);

      // Verify exception was logged
      sinon.assert.calledOnce(mockHubLogger.exceptionLogger);
    });

    it('should handle response without meta object', async () => {
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          value: [
            { timestamp: 1748599327715, level: 'INFO', message: 'Log without meta' }
          ]
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify response was sent successfully
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 0);
      assert.strictEqual(responseData.value[0].message, 'Log without meta');

      // Verify deviceLogEndPos was not updated
      assert.isUndefined(mockSessionKeyObj.deviceLogEndPos);
    });

    it('should handle empty value array in platform response', async () => {
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify response was sent successfully
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 0);
      assert.isArray(responseData.value);
      assert.strictEqual(responseData.value.length, 0);
    });

    it('should calculate correct timeout for idle_timeout > 20', async () => {
      mockSessionKeyObj.idle_timeout = 60;

      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify timeout was calculated correctly: (60 - 20) * 1000 = 40000
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.strictEqual(callArgs.timeout, 40000);
    });

    it('should calculate correct timeout for idle_timeout <= 20', async () => {
      mockSessionKeyObj.idle_timeout = 15;

      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify timeout was calculated correctly: (15 - 0) * 1000 = 15000
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.strictEqual(callArgs.timeout, 15000);
    });

    it('should handle missing deviceLogs property (undefined)', async () => {
      delete mockSessionKeyObj.deviceLogs;

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify error response was sent
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 13);
      assert.strictEqual(responseData.value.message, 'Device logs must be enabled for this session');
    });

    it('should properly encode URL parameters', async () => {
      mockSessionKeyObj.device = 'Samsung Galaxy S21+';
      mockSessionKeyObj.rails_session_id = 'session-with-special-chars@#$';

      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify URL encoding was applied
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.include(callArgs.path, 'device=Samsung%20Galaxy%20S21%2B');
      assert.include(callArgs.path, 'session_id=session-with-special-chars%40%23%24');
    });

    it('should handle platform response with missing value property', async () => {
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 }
          // Missing value property
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify response was sent with empty array
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 0);
      assert.isArray(responseData.value);
      assert.strictEqual(responseData.value.length, 0);
    });

    it('should handle request data with extra properties', async () => {
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({
        type: 'logcat',
        extraProperty: 'should be ignored',
        anotherProperty: 123
      });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify request was processed successfully despite extra properties
      sinon.assert.calledOnce(mockRequestlib.call);
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 0);
    });

    it('should handle zero idle_timeout', async () => {
      mockSessionKeyObj.idle_timeout = 0;

      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify timeout was calculated correctly: (0 - 0) * 1000 = 0
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.strictEqual(callArgs.timeout, 0);
    });

    it('should handle missing end_pos in platform response meta', async () => {
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0 }, // Missing end_pos
          value: [
            { timestamp: 1748599327715, level: 'INFO', message: 'Test log' }
          ]
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify response was sent successfully
      sinon.assert.calledOnce(mockBridge.sendResponse);
      const responseData = JSON.parse(mockRequestStateObj.data);
      assert.strictEqual(responseData.status, 0);

      // Verify deviceLogEndPos was not updated
      assert.isUndefined(mockSessionKeyObj.deviceLogEndPos);
    });

    it('should verify request headers are properly set', async () => {
      const platformResponse = {
        statusCode: 200,
        data: JSON.stringify({
          meta: { start_pos: 0, end_pos: 100 },
          value: []
        })
      };

      mockRequestlib.call.resolves(platformResponse);

      const handler = new DeviceLogHandler(mockSessionKeyObj, {}, {});
      const logData = JSON.stringify({ type: 'logcat' });

      await handler.processCommand(mockRequestStateObj, logData);

      // Verify request was made with correct headers
      const callArgs = mockRequestlib.call.getCall(0).args[0];
      assert.strictEqual(callArgs.hostname, 'test-host');
      assert.strictEqual(callArgs.port, 45671);
      assert.deepEqual(callArgs.headers, { 'Host': 'test-terminal' });
    });
  });
});